using System.Text;
using UnityEngine;
using UnityEngine.Networking;

namespace SmartVertex.Tools
{
    /// <summary>
    /// <PERSON>les sending text generation requests to the Google Gemini API and retrieving responses.
    /// </summary>
    public class GeminiAPI
    {
        private GeminiRequest requestPayload;
        private string apiKey;
        private string apiUrl;

        /// <summary>
        /// Gets or sets the Gemini request payload.
        /// </summary>
        public GeminiRequest RequestPayload
        {
            get => requestPayload;
            set => requestPayload = value;
        }

        /// <summary>
        /// Gets or sets the API key.
        /// </summary>
        public string ApiKey
        {
            get => apiKey;
            set => apiKey = value;
        }

        /// <summary>
        /// Gets or sets the API URL.
        /// </summary>
        public string ApiUrl
        {
            get => apiUrl;
            set => apiUrl = value;
        }

        /// <summary>
        /// Data container for Gemini request payload.
        /// </summary>
        [System.Serializable]
        public class GeminiRequest
        {
            public Content[] contents;
            public GenerationConfig generationConfig;
            public SafetySetting[] safetySettings;
        }

        /// <summary>
        /// Represents content in the request.
        /// </summary>
        [System.Serializable]
        public class Content
        {
            public Part[] parts;
            public string role;
        }

        /// <summary>
        /// Represents a part of the content.
        /// </summary>
        [System.Serializable]
        public class Part
        {
            public string text;
        }

        /// <summary>
        /// Configuration for text generation.
        /// </summary>
        [System.Serializable]
        public class GenerationConfig
        {
            public float temperature;
            public int topK;
            public float topP;
            public int maxOutputTokens;
            public string[] stopSequences;
        }

        /// <summary>
        /// Safety settings for content filtering.
        /// </summary>
        [System.Serializable]
        public class SafetySetting
        {
            public string category;
            public string threshold;
        }

        /// <summary>
        /// Response from Gemini API.
        /// </summary>
        [System.Serializable]
        public class GeminiResponse
        {
            public Candidate[] candidates;
            public UsageMetadata usageMetadata;
        }

        /// <summary>
        /// Candidate response from Gemini.
        /// </summary>
        [System.Serializable]
        public class Candidate
        {
            public Content content;
            public string finishReason;
            public int index;
            public SafetyRating[] safetyRatings;
        }

        /// <summary>
        /// Usage metadata from the response.
        /// </summary>
        [System.Serializable]
        public class UsageMetadata
        {
            public int promptTokenCount;
            public int candidatesTokenCount;
            public int totalTokenCount;
        }

        /// <summary>
        /// Safety rating for content.
        /// </summary>
        [System.Serializable]
        public class SafetyRating
        {
            public string category;
            public string probability;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class without parameters.
        /// </summary>
        public GeminiAPI()
        {
            // Default to Gemini 2.0 Flash model
            apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class with a request payload.
        /// </summary>
        public GeminiAPI(GeminiRequest requestPayload)
        {
            this.requestPayload = requestPayload;
            apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class with a request payload and API key.
        /// </summary>
        public GeminiAPI(GeminiRequest requestPayload, string apiKey)
        {
            this.requestPayload = requestPayload;
            this.apiKey = apiKey;
            apiUrl = "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent";
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="GeminiAPI"/> class with a request payload, API key, and API URL.
        /// </summary>
        public GeminiAPI(GeminiRequest requestPayload, string apiKey, string apiUrl)
        {
            this.requestPayload = requestPayload;
            this.apiKey = apiKey;
            this.apiUrl = apiUrl;
        }

        /// <summary>
        /// Generates text content from the Gemini request asynchronously.
        /// </summary>
        /// <returns>The generated response, or null on failure.</returns>
        public async Awaitable<GeminiResponse> Generate()
        {
            string responseJson = await SendGeminiRequestAsync();
            if (string.IsNullOrEmpty(responseJson))
                return null;

            try
            {
                return JsonUtility.FromJson<GeminiResponse>(responseJson);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to parse Gemini response: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Generates text content and returns only the text from the first candidate.
        /// </summary>
        /// <returns>The generated text, or null on failure.</returns>
        public async Awaitable<string> GenerateText()
        {
            GeminiResponse response = await Generate();
            if (response?.candidates != null && response.candidates.Length > 0)
            {
                var firstCandidate = response.candidates[0];
                if (firstCandidate?.content?.parts != null && firstCandidate.content.parts.Length > 0)
                {
                    return firstCandidate.content.parts[0].text;
                }
            }
            return null;
        }

        private async Awaitable<string> SendGeminiRequestAsync()
        {
            if (requestPayload == null)
            {
                Debug.LogError("Request payload is null");
                return null;
            }

            if (string.IsNullOrEmpty(apiKey))
            {
                Debug.LogError("API key is not set");
                return null;
            }

            string jsonPayload = JsonUtility.ToJson(requestPayload);
            byte[] bodyRaw = Encoding.UTF8.GetBytes(jsonPayload);

            using (var request = new UnityWebRequest(apiUrl, "POST"))
            {
                request.uploadHandler = new UploadHandlerRaw(bodyRaw);
                request.downloadHandler = new DownloadHandlerBuffer();
                request.SetRequestHeader("Content-Type", "application/json");
                request.SetRequestHeader("X-goog-api-key", apiKey);

                await request.SendWebRequest();

                if (request.result == UnityWebRequest.Result.Success)
                {
                    return request.downloadHandler.text;
                }
                else
                {
                    Debug.LogError($"Gemini API request failed: {request.error}");
                    Debug.LogError($"Response: {request.downloadHandler.text}");
                    return null;
                }
            }
        }

        /// <summary>
        /// Creates a simple text request for Gemini API.
        /// </summary>
        /// <param name="text">The text prompt to send.</param>
        /// <returns>A configured GeminiRequest.</returns>
        public static GeminiRequest CreateSimpleTextRequest(string text)
        {
            return new GeminiRequest
            {
                contents = new Content[]
                {
                    new Content
                    {
                        parts = new Part[]
                        {
                            new Part { text = text }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Creates a conversation request with system instructions and user message.
        /// </summary>
        /// <param name="systemInstruction">The system instruction.</param>
        /// <param name="userMessage">The user message.</param>
        /// <returns>A configured GeminiRequest.</returns>
        public static GeminiRequest CreateConversationRequest(string systemInstruction, string userMessage)
        {
            return new GeminiRequest
            {
                contents = new Content[]
                {
                    new Content
                    {
                        role = "user",
                        parts = new Part[]
                        {
                            new Part { text = systemInstruction + "\n\n" + userMessage }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Creates a request with custom generation configuration.
        /// </summary>
        /// <param name="text">The text prompt.</param>
        /// <param name="temperature">Controls randomness (0.0 to 1.0).</param>
        /// <param name="maxTokens">Maximum output tokens.</param>
        /// <returns>A configured GeminiRequest.</returns>
        public static GeminiRequest CreateConfiguredRequest(string text, float temperature = 0.7f, int maxTokens = 1000)
        {
            return new GeminiRequest
            {
                contents = new Content[]
                {
                    new Content
                    {
                        parts = new Part[]
                        {
                            new Part { text = text }
                        }
                    }
                },
                generationConfig = new GenerationConfig
                {
                    temperature = temperature,
                    maxOutputTokens = maxTokens,
                    topP = 0.8f,
                    topK = 10
                }
            };
        }
    }
}